# تطبيق Flutter

تطبيق Flutter جديد باللغة العربية.

## البدء

هذا المشروع هو نقطة انطلاق لتطبيق Flutter.

### المتطلبات

- Flutter SDK (الإصدار 3.0.0 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- Android SDK للتطوير على Android
- Xcode للتطوير على iOS (macOS فقط)

### تشغيل التطبيق

1. تأكد من تثبيت Flutter:
```bash
flutter doctor
```

2. احصل على التبعيات:
```bash
flutter pub get
```

3. شغل التطبيق:
```bash
flutter run
```

### الميزات

- واجهة مستخدم بسيطة باللغة العربية
- عداد تفاعلي
- تصميم Material Design
- دعم للأجهزة المحمولة

### البنية

```
lib/
  main.dart          # نقطة دخول التطبيق
android/             # ملفات Android
pubspec.yaml         # تبعيات المشروع
```

### التطوير

لإضافة ميزات جديدة:
1. أنشئ ملفات Dart جديدة في مجلد `lib/`
2. استورد الحزم المطلوبة في `pubspec.yaml`
3. استخدم `flutter hot reload` للتطوير السريع
